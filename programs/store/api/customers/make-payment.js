import _ from 'lodash';
import axios from 'axios';
import {trim} from 'framework/helpers';
import {findInstallment} from '../checkout/utils';
import {integrations} from '../../../finance/methods/online-pos-receipts';

export default async function (app, store, request, response) {
    const {
        customerId,
        amount,
        amountUSD,
        exchangeRate,
        installmentCount,
        cardBrand,
        cardNumber,
        cardHolder,
        expireYear,
        expireMonth,
        cvv,
        selectedCardBrand
    } = request.body;

    // Validate required fields for POS payment
    if (typeof cardNumber !== 'string' || cardNumber.length < 16) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Invalid card number'
        });
    }

    if (!customerId) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Customer ID is required'
        });
    }

    if (!amount || amount <= 0) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Amount must be greater than zero'
        });
    }

    if (!installmentCount || installmentCount < 1) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Installment count must be at least 1'
        });
    }

    if (!cardBrand) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Card brand is required'
        });
    }

    if (!cardHolder) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Card holder name is required'
        });
    }

    if (!expireYear || !expireMonth) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Card expiration date is required'
        });
    }

    if (!cvv) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'CVV is required'
        });
    }
    const cardInfoResponse = await axios({
        method: 'post',
        url: 'https://api.entererp.com/v1/common/bin-detail',
        headers: {
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({cardNumber})
    });
    const realCardBrand = cardInfoResponse.data.cardBrandCode;
    if (cardBrand !== realCardBrand) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'Provided card brand is not the same as real card brand!'
        });
    }

    // Get store currency to check its name
    const storeCurrency = await app.collection('kernel.currencies').findOne({
        _id: store.currencyId,
        $select: ['name'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });

    let usdCurrency = null;
    if (amountUSD) {
        usdCurrency = await app.collection('kernel.currencies').findOne({
            name: 'USD',
            $select: ['_id'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
    }

    let finalAmount = amount;
    let posAmount = amount; // POS'tan çekilecek TL tutarı
    let isUsingForeignCurrency = false;
    let currencyId = store.currencyId;
    let currencyRate = 1;

    if (amount && amountUSD && exchangeRate) {
        if (storeCurrency.name !== 'TRY' && amountUSD > 0) {
            finalAmount = amountUSD; // Muhasebe kaydında döviz tutarı
            posAmount = amount; // POS'tan TL olarak çekilecek tutar
            currencyId = usdCurrency._id;
            currencyRate = exchangeRate;
            isUsingForeignCurrency = true;
        } else {
            finalAmount = amount;
            posAmount = amount;
            currencyId = store.currencyId;
            currencyRate = 1;
        }
    } else if (amountUSD && !amount) {
        finalAmount = amountUSD; // Muhasebe kaydında döviz tutarı
        posAmount = amountUSD * (exchangeRate || 1); // POS'tan TL olarak çekilecek tutar
        currencyId = usdCurrency._id;
        currencyRate = exchangeRate || 1;
        isUsingForeignCurrency = true;
    }

    const installment = await findInstallment(
        app,
        store,
        cardBrand,
        installmentCount,
        posAmount,
        selectedCardBrand,
        customerId
    );

    // Get pos.
    const pos = await app.collection('accounting.pos').findOne({
        _id: installment.posId,
        $disableActiveCheck: true,
        $disableSoftDelete: true
    });
    if (!pos || !pos.integrationType) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'POS not found!'
        });
    }

    // Validate POS configuration
    if (!pos.journalId) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'POS journal account is not configured!'
        });
    }

    if (!Array.isArray(pos.commissions) || pos.commissions.length === 0) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'POS commissions are not configured!'
        });
    }

    if (!Array.isArray(pos.installmentPayments) || pos.installmentPayments.length === 0) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: 'POS installment payments are not configured!'
        });
    }

    // Apply proper POS calculation logic similar to detail.vue
    const currencyPrecision = 2; // app.setting('system.currencyPrecision')
    const round = app.roundNumber;

    // Find the commission for this installment
    const commission = pos.commissions.find(c => c.installment === installmentCount);
    if (!commission) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: `Commission not found for installment count ${installmentCount}!`
        });
    }

    // Validate commission limits
    if (_.isNumber(commission.lowerLimit) && posAmount < commission.lowerLimit) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: `Amount ${posAmount} is below minimum limit ${commission.lowerLimit} for ${installmentCount} installments`
        });
    }

    if (_.isNumber(commission.upperLimit) && posAmount > commission.upperLimit) {
        return response.status(400).json({
            status: 'error',
            code: 400,
            message: `Amount ${posAmount} is above maximum limit ${commission.upperLimit} for ${installmentCount} installments`
        });
    }

    // Calculate due difference and total using the same logic as detail.vue
    let dueDifference = 0;
    let total = posAmount;
    let calculatedInstallmentAmount = 0;

    // Calculate commission-based due difference
    if (pos.commissionType !== 'within') {
        dueDifference = round(
            (posAmount * (commission.commission || 0)) / 100 +
                (posAmount * (commission.serviceCommission || 0)) / 100,
            currencyPrecision
        );
    }

    total = posAmount + dueDifference;

    // Calculate interest rate if applicable
    if (
        _.isNumber(commission.interestRate) &&
        commission.interestRate > 0 &&
        pos.commissionType !== 'within'
    ) {
        // Calculate due day difference (simplified version)
        let dueDayDifference = 0;
        if (pos.paymentOnSpecificDate) {
            // Use cutoff date logic
            const now = app.datetime.local().startOf('day');
            const cutoffDate = pos.cutoffDate;

            if (now.day > cutoffDate) {
                dueDayDifference = now.plus({months: 1}).set({day: cutoffDate}).diff(now, 'days');
            } else {
                dueDayDifference = now.set({day: cutoffDate}).diff(now, 'days');
            }
        } else {
            // Use installment payment refund days
            const installmentPayment = pos.installmentPayments.find(p => p.installment === installmentCount);
            if (installmentPayment) {
                dueDayDifference = installmentPayment.refund || 0;
            }
        }

        const interestDiff = round(
            ((total * commission.interestRate) / 3000) * dueDayDifference,
            currencyPrecision
        );

        dueDifference += interestDiff;
        total += interestDiff;
    }

    calculatedInstallmentAmount = round(total / installmentCount, currencyPrecision);

    // Update installment object with calculated values
    installment.dueDifference = dueDifference;
    installment.total = total;
    installment.installmentAmount = calculatedInstallmentAmount;

    // Prepare financial entry payload.
    const bankAccount = await app.collection('accounting.bank-accounts').findOne({
        _id: pos.bankAccountId,
        $disableBranchCheck: true
    });
    const paymentJournal = await app.collection('accounting.journals').findOne({
        _id: bankAccount.journalId,
        $disableBranchCheck: true,
        $select: ['_id']
    });
    const posJournal = await app.collection('accounting.journals').findOne({
        _id: pos.journalId,
        $disableBranchCheck: true,
        $select: ['shortDescription']
    });
    const now = app.datetime.local().startOf('day');
    const entry = {};
    entry.status = 'draft';
    entry.type = 'receipt';
    entry.documentType = 'pos';
    entry.journalId = pos.journalId;
    entry.paymentAccountId = paymentJournal._id;
    entry.posId = pos._id;
    entry.partnerType = 'customer';
    entry.partnerId = customerId;
    entry.amount = posAmount;
    entry.amountUSD = amountUSD;
    entry.baseTotal = posAmount;
    entry.dueDifference = installment.dueDifference; // Use calculated dueDifference
    entry.total = installment.total; // Use calculated total
    entry.installmentCount = installmentCount;
    if (_.isFinite(installment.plusInstallmentCount)) {
        entry.plusInstallmentCount = installment.plusInstallmentCount;
    }
    entry.installmentAmount = installment.installmentAmount; // Use calculated installmentAmount
    entry.branchId = store.branchId;
    entry.currencyId = currencyId;
    entry.currencyRate = currencyRate;
    entry.exchangeRateUsed = exchangeRate;
    entry.isUsingForeignCurrency = isUsingForeignCurrency;
    entry.calculateDueDifference = true; // Add missing field for POS calculations
    entry.reference = '';
    entry.description = posJournal.shortDescription || app.translate('EnterStore online POS receipt.');
    entry.recordDate = app.datetime.local().toJSDate();
    entry.issueDate = now.toJSDate();
    entry.dueDate = now.toJSDate();

    // Add card information fields required for POS transactions
    entry.cardBrand = cardBrand;
    entry.cardHolder = cardHolder;
    entry.cardNumber = cardNumber;
    entry.expireMonth = expireMonth;
    entry.expireYear = expireYear;
    entry.cvv = cvv;
    if (selectedCardBrand) {
        entry.selectedCardBrand = selectedCardBrand;
    }

    // Add global currency rate for proper accounting
    entry.globalCurrencyRate = 1;
    // Calculate due date using the same logic as detail.vue and approve-entry.js
    if (pos.paymentOnSpecificDate) {
        const cutoffDate = pos.cutoffDate;

        if (now.day > cutoffDate) {
            entry.dueDate = now.plus({months: 1}).set({day: cutoffDate}).toJSDate();
        } else {
            entry.dueDate = now.set({day: cutoffDate}).toJSDate();
        }

        // For multiple installments, add months for the final due date
        entry.dueDate = app.datetime
            .fromJSDate(entry.dueDate)
            .plus({months: entry.installmentCount - 1})
            .toJSDate();
    } else {
        // Use installment payment refund days
        const installmentPayment = pos.installmentPayments.find(c => c.installment === entry.installmentCount);

        if (!!installmentPayment) {
            entry.dueDate = now.plus({days: installmentPayment.refund || 0}).toJSDate();
        }
    }

    // Get user.
    const user = await app.collection('kernel.users').findOne({isRoot: true});

    // Get currency.
    const currency = await app.collection('kernel.currencies').findOne({
        _id: store.currencyId,
        $select: ['name'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });

    // Prepare partner.
    const customer = await app.collection('kernel.partners').findOne({
        _id: customerId,
        $select: ['name', 'email', 'phone', 'address'],
        $disableSoftDelete: true,
        $disableActiveCheck: true
    });
    const partner = {
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address
    };

    // Get result
    const url = store.website || '';
    const data = {
        cardBrand,
        cardNumber,
        cardHolder,
        expireMonth,
        expireYear,
        cvv,
        amount: installment.total, // Use calculated total amount
        installmentCount,
        posId: pos._id,
        partnerId: customerId,
        currencyId: currency._id,
        currencyRate: currencyRate, // Use proper currency rate
        description: posJournal.shortDescription || app.translate('EnterStore online POS receipt.'),
        pos,
        partner,
        currency,
        storeId: store._id,
        branchId: store.branchId,
        tutar: posAmount,
        tutar_sbp: finalAmount,
        exchangeRateUsed: exchangeRate,
        isUsingForeignCurrency: isUsingForeignCurrency,
        evaluateAccountCurrency: isUsingForeignCurrency,
        globalCurrencyRate: 1,

        // Add calculated POS values
        baseTotal: posAmount,
        dueDifference: installment.dueDifference,
        installmentAmount: installment.installmentAmount,
        calculateDueDifference: true,

        ...(pos.integrationType === 'paytr'
            ? {
                  okUrl: `${trim(url, '/')}/api/checkout/paytr-success`,
                  failUrl: `${trim(url, '/')}/api/checkout/paytr-error`,
                  frameUrl: `${trim(url, '/')}/api/checkout/paytr-frame-3d`
              }
            : {}),
        entryPayload: entry
    };

    const result = await integrations[pos.integrationType].charge(app, data, {user});

    return response.json({
        customerName: partner.name,
        cardBrandCode: installment.cardBrandCode,
        cardBrandName: installment.cardBrandName,
        cardBrandLogo: installment.cardBrandLogo,
        installmentCount: installment.installmentCount,
        plusInstallmentCount: installment.plusInstallmentCount,
        installmentRate: installment.installmentRate,
        installmentAmount: installment.installmentAmount, // Now properly calculated
        total: installment.total, // Now properly calculated with commissions
        dueDifference: installment.dueDifference, // Add calculated due difference
        baseTotal: posAmount, // Original amount before commissions
        amountTL: posAmount, // POS'tan çekilen TL tutarı
        amountUSD: finalAmount, // Muhasebe kaydında döviz tutarı
        exchangeRate: exchangeRate,
        currencyUsed: currencyId,
        isUsingForeignCurrency: isUsingForeignCurrency,
        ...result
    });
}
